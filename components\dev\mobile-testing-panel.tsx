'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useMobile, useMobilePerformance } from '@/hooks/use-mobile';

interface MobileTestingPanelProps {
  enabled?: boolean;
}

export function MobileTestingPanel({ enabled = process.env.NODE_ENV === 'development' }: MobileTestingPanelProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [testResults, setTestResults] = useState<any[]>([]);
  const mobile = useMobile();
  const performance = useMobilePerformance();

  useEffect(() => {
    if (!enabled) return;

    // Run mobile compatibility tests
    const runTests = () => {
      const tests = [
        {
          name: 'Touch Target Size',
          test: () => {
            const buttons = document.querySelectorAll('button, a, [role="button"]');
            const failedElements: Element[] = [];
            
            buttons.forEach(button => {
              const rect = button.getBoundingClientRect();
              if (rect.width < 44 || rect.height < 44) {
                failedElements.push(button);
              }
            });
            
            return {
              passed: failedElements.length === 0,
              message: failedElements.length > 0 
                ? `${failedElements.length} elements below 44px touch target`
                : 'All touch targets meet minimum size requirements'
            };
          }
        },
        {
          name: 'Horizontal Scroll',
          test: () => {
            const hasHorizontalScroll = document.body.scrollWidth > window.innerWidth;
            return {
              passed: !hasHorizontalScroll,
              message: hasHorizontalScroll 
                ? 'Page has horizontal scroll - check responsive layout'
                : 'No horizontal scroll detected'
            };
          }
        },
        {
          name: 'Viewport Meta Tag',
          test: () => {
            const viewportMeta = document.querySelector('meta[name="viewport"]');
            const hasViewport = viewportMeta !== null;
            return {
              passed: hasViewport,
              message: hasViewport 
                ? 'Viewport meta tag present'
                : 'Missing viewport meta tag'
            };
          }
        },
        {
          name: 'Mobile Performance',
          test: () => {
            const animationCount = document.querySelectorAll('[style*="animation"], .animate-').length;
            const imageCount = document.querySelectorAll('img').length;
            const performanceScore = 100 - (animationCount * 2) - (imageCount * 0.5);
            
            return {
              passed: performanceScore > 70,
              message: `Performance score: ${Math.round(performanceScore)}/100`
            };
          }
        },
        {
          name: 'Text Readability',
          test: () => {
            const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6');
            const tooSmallText: Element[] = [];
            
            textElements.forEach(element => {
              const styles = window.getComputedStyle(element);
              const fontSize = parseFloat(styles.fontSize);
              if (fontSize < 14) {
                tooSmallText.push(element);
              }
            });
            
            return {
              passed: tooSmallText.length === 0,
              message: tooSmallText.length > 0 
                ? `${tooSmallText.length} elements with text smaller than 14px`
                : 'All text meets minimum size requirements'
            };
          }
        }
      ];

      const results = tests.map(test => ({
        ...test,
        result: test.test()
      }));

      setTestResults(results);
    };

    // Run tests after a delay to allow page to load
    const timer = setTimeout(runTests, 2000);
    return () => clearTimeout(timer);
  }, [enabled]);

  if (!enabled) return null;

  return (
    <>
      {/* Toggle Button */}
      <motion.button
        className="fixed bottom-4 right-4 z-[9999] bg-purple-600 text-white p-3 rounded-full shadow-lg"
        onClick={() => setIsOpen(!isOpen)}
        whileTap={{ scale: 0.95 }}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1 }}
      >
        📱
      </motion.button>

      {/* Testing Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed inset-0 z-[9998] bg-black/50 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsOpen(false)}
          >
            <motion.div
              className="absolute bottom-0 left-0 right-0 bg-white rounded-t-xl p-6 max-h-[80vh] overflow-y-auto"
              initial={{ y: '100%' }}
              animate={{ y: 0 }}
              exit={{ y: '100%' }}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900">Mobile Testing Panel</h2>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </button>
              </div>

              {/* Device Info */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-2">Device Information</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>Screen: {mobile.screenWidth}×{mobile.screenHeight}</div>
                  <div>DPR: {mobile.devicePixelRatio}</div>
                  <div>Mobile: {mobile.isMobile ? 'Yes' : 'No'}</div>
                  <div>Touch: {mobile.isTouchDevice ? 'Yes' : 'No'}</div>
                  <div>Orientation: {mobile.orientation}</div>
                  <div>Low-end: {performance.isLowEndDevice ? 'Yes' : 'No'}</div>
                </div>
              </div>

              {/* Performance Settings */}
              <div className="mb-6 p-4 bg-blue-50 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-2">Performance Settings</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>Reduce Motion: {performance.shouldReduceMotion ? 'Yes' : 'No'}</div>
                  <div>Image Quality: {performance.imageQuality}%</div>
                  <div>Particles: {performance.particleCount}</div>
                  <div>Frame Rate: {performance.animationFrameRate}fps</div>
                  <div>Blur Effects: {performance.enableBlur ? 'Yes' : 'No'}</div>
                  <div>Shadows: {performance.enableShadows ? 'Yes' : 'No'}</div>
                </div>
              </div>

              {/* Test Results */}
              <div className="mb-6">
                <h3 className="font-semibold text-gray-900 mb-3">Test Results</h3>
                <div className="space-y-2">
                  {testResults.map((test, index) => (
                    <div
                      key={index}
                      className={`p-3 rounded-lg border ${
                        test.result.passed
                          ? 'bg-green-50 border-green-200 text-green-800'
                          : 'bg-red-50 border-red-200 text-red-800'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{test.name}</span>
                        <span className="text-sm">
                          {test.result.passed ? '✅' : '❌'}
                        </span>
                      </div>
                      <div className="text-sm mt-1">{test.result.message}</div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Quick Actions */}
              <div className="space-y-2">
                <button
                  onClick={() => window.location.reload()}
                  className="w-full p-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
                >
                  Reload Page
                </button>
                <button
                  onClick={() => {
                    const results = testResults.map(test => 
                      `${test.name}: ${test.result.passed ? 'PASS' : 'FAIL'} - ${test.result.message}`
                    ).join('\n');
                    navigator.clipboard.writeText(results);
                  }}
                  className="w-full p-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  Copy Results
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}

// Hook for mobile testing utilities
export function useMobileTesting() {
  const mobile = useMobile();
  const performance = useMobilePerformance();

  const simulateTouch = (element: Element) => {
    const touchEvent = new TouchEvent('touchstart', {
      bubbles: true,
      cancelable: true,
      touches: [
        new Touch({
          identifier: 0,
          target: element,
          clientX: 0,
          clientY: 0,
          radiusX: 0,
          radiusY: 0,
          rotationAngle: 0,
          force: 1
        })
      ]
    });
    element.dispatchEvent(touchEvent);
  };

  const measureTouchTarget = (element: Element) => {
    const rect = element.getBoundingClientRect();
    return {
      width: rect.width,
      height: rect.height,
      meetsMinimum: rect.width >= 44 && rect.height >= 44
    };
  };

  const checkScrollPerformance = () => {
    let frameCount = 0;
    let lastTime = performance.now();
    
    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        frameCount = 0;
        lastTime = currentTime;
        return fps;
      }
      
      requestAnimationFrame(measureFPS);
      return null;
    };
    
    return measureFPS();
  };

  return {
    mobile,
    performance,
    simulateTouch,
    measureTouchTarget,
    checkScrollPerformance
  };
}
